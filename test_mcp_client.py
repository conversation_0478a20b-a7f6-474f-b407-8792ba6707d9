#!/usr/bin/env python3
"""
Simple test client to verify MCP server functionality.
Tests the tools/list, prompts/list, and resources/list methods.
"""

import asyncio
import json
import aiohttp
from typing import Dict, Any


async def send_mcp_request(method: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
    """Send an MCP request to the server."""
    if params is None:
        params = {}

    request_data = {
        "jsonrpc": "2.0",
        "method": method,
        "params": params,
        "id": 1
    }

    url = "http://localhost:8002/mcp/"

    # Add required headers for MCP
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json, text/event-stream"
    }

    async with aiohttp.ClientSession() as session:
        async with session.post(url, json=request_data, headers=headers) as response:
            if response.status == 200:
                return await response.json()
            else:
                return {
                    "error": {
                        "code": response.status,
                        "message": f"HTTP {response.status}: {await response.text()}"
                    }
                }


async def test_mcp_methods():
    """Test the main MCP methods."""
    methods_to_test = [
        "tools/list",
        "prompts/list",
        "resources/list"
    ]

    print("Testing MCP server methods...")
    print("=" * 50)

    for method in methods_to_test:
        print(f"\nTesting {method}...")
        try:
            result = await send_mcp_request(method)

            if "error" in result:
                print(f"❌ ERROR: {result['error']}")
            else:
                print(f"✅ SUCCESS: {json.dumps(result, indent=2)}")

        except Exception as e:
            print(f"❌ EXCEPTION: {e}")

    print("\n" + "=" * 50)
    print("Test completed!")


async def test_original_client_behavior():
    """Test the original client behavior that was causing 'Method not found' errors."""
    print("\nTesting original client behavior (like the logs you showed)...")
    print("=" * 60)

    # Simulate the original client requests with specific IDs
    test_requests = [
        {"method": "tools/list", "id": 1},
        {"method": "prompts/list", "id": 2},
        {"method": "resources/list", "id": 3}
    ]

    for req in test_requests:
        print(f"\nSending {req['method']} with id={req['id']}...")
        try:
            result = await send_mcp_request(req["method"], {})

            if "error" in result:
                error_code = result["error"].get("code", "unknown")
                error_message = result["error"].get("message", "unknown")
                print(f"❌ ERROR (id={req['id']}): code={error_code}, message='{error_message}'")
            else:
                print(f"✅ SUCCESS (id={req['id']}): Method found and working!")

        except Exception as e:
            print(f"❌ EXCEPTION (id={req['id']}): {e}")

    print("\n" + "=" * 60)


if __name__ == "__main__":
    asyncio.run(test_mcp_methods())
    asyncio.run(test_original_client_behavior())
