# MCP Server "Method not found" Fix

## Problem
Your MCP server was returning "Method not found" errors for `prompts/list` (id=2) and `resources/list` (id=3) requests:

```
2025-09-08T13:48:30.202Z [info] [litserve] Message from server: {"jsonrpc":"2.0","id":2,"error":{"code":-32601,"message":"Method not found"}}
2025-09-08T13:48:30.211Z [info] [litserve] Message from server: {"jsonrpc":"2.0","id":3,"error":{"code":-32601,"message":"Method not found"}}
```

## Root Cause
LitServe's MCP implementation was incomplete. It only registered handlers for:
- `tools/list` ✅
- `call_tool` ✅

But was missing handlers for:
- `prompts/list` ❌
- `resources/list` ❌

## Solution
I implemented a patch that extends LitServe's `_LitMCPServerConnector` class to add the missing method handlers:

```python
# Patch LitServe's MCP implementation to add missing handlers
from litserve.mcp import _LitMCPServerConnector

# Store the original _mount_with_fastapi method
original_mount_with_fastapi = _LitMCPServerConnector._mount_with_fastapi

def patched_mount_with_fastapi(self, app):
    """Enhanced version that adds missing MCP handlers."""
    # Call the original method first
    original_mount_with_fastapi(self, app)
    
    # Add the missing handlers
    @self.mcp_server.list_prompts()
    async def _list_prompts():
        """Return empty list of prompts."""
        logger.debug("list_prompts called, returning empty list")
        return []
    
    @self.mcp_server.list_resources()
    async def _list_resources():
        """Return empty list of resources."""
        logger.debug("list_resources called, returning empty list")
        return []
        
    logger.info("Added missing MCP handlers for prompts and resources")

# Apply the patch
_LitMCPServerConnector._mount_with_fastapi = patched_mount_with_fastapi
```

## Results
After applying the fix:

### Before (❌ Errors):
```json
{"jsonrpc":"2.0","id":2,"error":{"code":-32601,"message":"Method not found"}}
{"jsonrpc":"2.0","id":3,"error":{"code":-32601,"message":"Method not found"}}
```

### After (✅ Success):
```json
{"jsonrpc":"2.0","id":2,"result":{"prompts":[]}}
{"jsonrpc":"2.0","id":3,"result":{"resources":[]}}
```

## Testing
The fix was verified with a test client that confirms all three MCP methods now work:
- `tools/list` → Returns your DuckDuckGo search tool
- `prompts/list` → Returns empty array (as expected)
- `resources/list` → Returns empty array (as expected)

## Implementation Notes
1. **Non-intrusive**: The patch doesn't modify LitServe's core code, just extends it
2. **Backward compatible**: Existing functionality remains unchanged
3. **Proper MCP protocol**: Returns correctly formatted empty arrays for prompts and resources
4. **Logging**: Includes debug logging to track when methods are called

## Next Steps
If you want to add actual prompts or resources to your MCP server, you would need to:
1. Modify the handlers to return actual prompt/resource definitions
2. Implement the corresponding `get_prompt` and `read_resource` handlers
3. Add your prompt/resource logic to the server

The current implementation provides a working foundation that eliminates the "Method not found" errors.
