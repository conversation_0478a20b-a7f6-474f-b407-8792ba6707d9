from pydantic import BaseModel
from typing import List
import logging

import litserve as ls
from litserve.mcp import MCP
from litserve.utils import configure_logging

# https://github.com/deedy5/duckduckgo_search
from ddgs import DDGS

# Configure LitServe logging with Rich formatting (optional)
configure_logging(level=logging.INFO, use_rich=True)
logger = logging.getLogger("litserve")


class AddRequest(BaseModel):
    a: int
    b: int


class TextRequest(BaseModel):
    text: str


class AddAPI(ls.LitAPI):
    def setup(self, device):
        pass

    def decode_request(self, request: AddRequest, **kwargs):
        return request

    def predict(self, x: AddRequest, *args, **kwargs):
        return {"result": x.a + x.b}


class ReverseAPI(ls.LitAPI):
    def setup(self, device):
        pass

    def decode_request(self, request: TextRequest, **kwargs):
        return request

    def predict(self, x: TextRequest, **kwargs):
        return {"reversed": x.text[::-1]}


class UpperAPI(ls.LitAPI):
    def setup(self, device):
        pass

    def decode_request(self, request: TextRequest, **kwargs):
        return request

    def predict(self, x: TextRequest, **kwargs):
        return {"upper": x.text.upper()}


class WebSearchResult(BaseModel):
    url: str
    title: str
    description: str | None = None


class WebSearchResponse(BaseModel):
    results: List[WebSearchResult]


class DuckDuckGoSearchAPI(ls.LitAPI):
    def setup(self, device):
        pass

    def decode_request(self, request, **kwargs):
        # Use LitServe's logging system instead of standard logger
        self.log("request_received", str(request))
        self.log("request_type", str(type(request)))
        self.log("request_kwargs", str(kwargs))

        return request

    def predict(self, x, **kwargs):
        self.log("search_query", x)

        return WebSearchResponse(results=[
            WebSearchResult(url="https://www.google.com", title="Google", description="Google it!")
        ])


if __name__ == "__main__":
    logger.info("Starting MCP Server...")

    add_mcp = MCP(
        name="add_numbers", description="Ajoute deux nombres et retourne le résultat."
    )
    reverse_mcp = MCP(
        name="reverse_string", description="Inverse une chaîne de caractères donnée."
    )
    upper_mcp = MCP(
        name="uppercase_string",
        description="Convertit une chaîne de caractères donnée en majuscules.",
    )

    duckduckgo_mcp = MCP(
        name="duckduckgo_search",
        description="Effectue une recherche sur DuckDuckGo et retourne les résultats.",
    )
    """input_schema={
            "type": "object",
            "properties": {
                "text": {"type": "string", "description": "Texte de la recherche"}
            },
            "required": ["text"],
        },"""

    logger.info("Configuring MCP server...")
    server = ls.LitServer(
        [
            # AddAPI(mcp=add_mcp, api_path="/add"),
            # ReverseAPI(mcp=reverse_mcp, api_path="/reverse"),
            # UpperAPI(mcp=upper_mcp, api_path="/upper"),
            DuckDuckGoSearchAPI(mcp=duckduckgo_mcp, api_path="/websearch"),
        ],
    )

    # Patch LitServe's MCP implementation to add missing handlers
    # This fixes the "Method not found" errors for prompts/list and resources/list
    from litserve.mcp import _LitMCPServerConnector

    # Store the original _mount_with_fastapi method
    original_mount_with_fastapi = _LitMCPServerConnector._mount_with_fastapi

    def patched_mount_with_fastapi(self, app):
        """Enhanced version that adds missing MCP handlers."""
        # Call the original method first
        original_mount_with_fastapi(self, app)

        # Add the missing handlers
        @self.mcp_server.list_prompts()
        async def _list_prompts():
            """Return empty list of prompts."""
            logger.debug("list_prompts called, returning empty list")
            return []

        @self.mcp_server.list_resources()
        async def _list_resources():
            """Return empty list of resources."""
            logger.debug("list_resources called, returning empty list")
            return []

        logger.info("Added missing MCP handlers for prompts and resources")

    # Apply the patch
    _LitMCPServerConnector._mount_with_fastapi = patched_mount_with_fastapi

    # lsof -ti:8002 | xargs kill -9
    logger.info("Starting server on port 8002...")
    server.run(port=8002)
